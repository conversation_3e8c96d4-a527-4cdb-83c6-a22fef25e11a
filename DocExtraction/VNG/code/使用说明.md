# VNG病例信息提取系统使用说明

## 项目概述

VNG病例信息提取系统是一个专门用于从VNG（视频眼震电图）病例文档中提取患者信息和检查数据的工具。系统能够批量处理docx、doc、wps格式的病例文件，自动提取患者基本信息、检查结果和诊断信息，并生成结构化的CSV数据文件。

## 文件结构

```
DocExtraction/VNG/code/
├── 需求文档.md                    # 详细需求文档
├── vng_data_extractor.py          # 主要提取程序
├── test_extractor.py              # 完整测试脚本
├── simple_test.py                 # 简单测试脚本
├── 使用说明.md                    # 本文档
└── extract_patient_info.py        # 原有的提取脚本（参考）

DocExtraction/VNG/output/
├── vng_patient_data_YYYYMMDD_HHMMSS.csv    # 提取的数据文件
├── processing_log_YYYYMMDD_HHMMSS.txt      # 处理日志
├── statistics_report.txt                   # 统计报告
└── failed_files/                           # 处理失败的文件
    ├── cannot_extract/                     # 无法提取信息的文件
    └── format_error/                       # 格式错误的文件
```

## 使用方法

### 1. 基本使用

```bash
# 使用默认路径处理所有文件
python vng_data_extractor.py

# 指定源目录和输出目录
python vng_data_extractor.py --source-dir /path/to/source --output-dir /path/to/output

# 开启调试模式查看详细信息
python vng_data_extractor.py --debug
```

### 2. 参数说明

- `--source-dir`: 源文件目录路径（默认：final_vng_classified目录）
- `--output-dir`: 输出目录路径（默认：VNG/output目录）
- `--debug`: 显示详细的调试信息

### 3. 测试功能

```bash
# 运行完整测试
python test_extractor.py

# 运行简单测试
python simple_test.py
```

## 提取的数据字段

### 患者基本信息
- **文件名**: 原始文件名
- **年份**: 检查年份
- **姓名**: 患者姓名
- **性别**: 男/女
- **年龄**: 患者年龄（岁）
- **检查日期**: VNG检查日期
- **科别**: 就诊科室
- **门诊住院号**: 患者就诊编号
- **编号**: 检查编号

### VNG检查结果
- **定标试验**: 正常/异常
- **自发性眼震**: 无/有
  - 水平向左/右百分比
  - 垂直向上/下百分比
- **凝视试验**: 正常/异常
- **平滑跟踪**: Ⅰ型/Ⅱ型/Ⅲ型/Ⅳ型
- **扫视试验**: 正常/异常（过冲/欠冲）
- **视动性眼震**: 对称/不对称

### 位置试验
- **Roll Test**: 阴性/阳性
- **翻身试验**: 阴性/阳性
- **Dix-Hallpike左侧**: 阴性/阳性
- **Dix-Hallpike右侧**: 阴性/阳性
- **疲劳现象**: 阴性/阳性

### 诊断信息
- **印象**: 医生的诊断意见
- **检查者**: 执行检查的医生
- **原始文本**: 文档原始内容（前500字符）

## 处理结果

### 成功处理的文件
成功提取的数据会保存到CSV文件中，文件名格式为：`vng_patient_data_YYYYMMDD_HHMMSS.csv`

### 处理失败的文件
失败的文件会被分类移动到不同的文件夹：

1. **cannot_extract/**: 无法提取信息的文件
   - 非VNG报告文件
   - 文本提取失败的文件

2. **format_error/**: 格式错误的文件
   - 缺少必要字段的文件
   - 数据验证失败的文件

## 最新处理结果

根据2025年8月2日的处理结果：

- **总文件数**: 7,307个
- **成功提取**: 712个（9.7%）
- **无法提取**: 425个
- **格式错误**: 6,170个

### 按年份统计
- 2018年: 41个文件
- 2019年: 102个文件
- 2020年: 3个文件
- 2023年: 220个文件
- 2024年: 346个文件

## 注意事项

### 1. 文件格式支持
- **DOCX格式**: 支持最好，提取成功率最高
- **DOC格式**: 部分支持，依赖olefile库
- **WPS格式**: 部分支持，可能存在兼容性问题

### 2. 数据质量
- 系统会验证提取数据的完整性
- 缺少关键字段（姓名、性别、年龄、检查日期）的记录会被标记为格式错误
- 建议人工检查format_error文件夹中的文件

### 3. 性能考虑
- 处理大量文件时建议使用调试模式监控进度
- 平均处理速度约为每秒数十个文件
- 内存使用量相对较低

## 故障排除

### 1. 导入错误
如果遇到模块导入错误，请安装必要的依赖：
```bash
pip install python-docx olefile
```

### 2. 文件路径问题
确保源目录路径正确，文件具有读取权限

### 3. 输出目录权限
确保输出目录具有写入权限

### 4. 内存不足
如果处理大量文件时内存不足，可以考虑分批处理

## 扩展功能

### 1. 自定义提取规则
可以修改`vng_data_extractor.py`中的正则表达式来适配不同的文档格式

### 2. 添加新字段
在CSV头部和提取逻辑中添加新的数据字段

### 3. 改进文档处理
针对特定格式的文档优化文本提取逻辑

## 联系支持

如有问题或需要改进建议，请查看代码注释或联系开发团队。
