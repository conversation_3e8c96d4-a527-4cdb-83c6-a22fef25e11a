#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
from vng_data_extractor import VNGDataExtractor

def main():
    # 测试单个文件
    test_file = '../data/final_vng_classified/2018/146 梁趣轩.docx'
    output_dir = '../test_output'
    os.makedirs(output_dir, exist_ok=True)

    print("测试VNG数据提取器...")
    print(f"测试文件: {test_file}")
    
    extractor = VNGDataExtractor('', output_dir, debug=True)
    patient_data = extractor.extract_patient_data(test_file)

    if patient_data:
        print('\n提取成功！')
        print('-' * 50)
        for key, value in patient_data.items():
            if key != '原始文本':
                print(f'{key}: {value}')
        print('-' * 50)
        
        # 验证数据
        is_valid, missing_fields = extractor.validate_data(patient_data)
        print(f'数据验证: {"通过" if is_valid else "失败"}')
        if not is_valid:
            print(f'缺失字段: {missing_fields}')
    else:
        print('提取失败')

if __name__ == "__main__":
    main()
