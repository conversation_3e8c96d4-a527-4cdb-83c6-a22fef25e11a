#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建清洁版本的CSV文件

从原始提取结果中创建一个更清洁、更易读的CSV文件
"""

import pandas as pd
import os
from datetime import datetime

def create_clean_csv():
    """创建清洁版本的CSV文件"""
    
    # 读取原始CSV文件（最新的）
    input_file = '../output/vng_patient_data_20250802_210204.csv'
    
    if not os.path.exists(input_file):
        print(f"原始CSV文件不存在: {input_file}")
        return
    
    try:
        # 读取数据
        df = pd.read_csv(input_file)
        print(f"读取到 {len(df)} 行数据")
        
        # 选择主要字段，去掉原始文本
        main_columns = [
            '文件名', '年份', '姓名', '性别', '年龄', '检查日期', '科别', 
            '门诊住院号', '编号', '定标试验', '自发性眼震', 
            '自发性眼震_水平向左', '自发性眼震_水平向右', 
            '自发性眼震_垂直向上', '自发性眼震_垂直向下',
            '凝视试验', '平滑跟踪', '扫视试验', '视动性眼震',
            'Roll_Test', '翻身试验', 'Dix_Hallpike_左侧', 
            'Dix_Hallpike_右侧', '疲劳现象', '印象', '检查者'
        ]
        
        # 创建清洁版本
        clean_df = df[main_columns].copy()
        
        # 替换空值为空字符串
        clean_df = clean_df.fillna('')
        
        # 保存清洁版本
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f'../output/vng_patient_data_clean_{timestamp}.csv'
        
        clean_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"清洁版本CSV已保存: {output_file}")
        
        # 显示统计信息
        print(f"\n数据统计:")
        print(f"总病例数: {len(clean_df)}")
        print(f"字段数: {len(clean_df.columns)}")
        
        # 按年份统计
        year_stats = clean_df['年份'].value_counts().sort_index()
        print(f"\n按年份统计:")
        for year, count in year_stats.items():
            print(f"  {year}年: {count} 个病例")
        
        # 按性别统计
        gender_stats = clean_df['性别'].value_counts()
        print(f"\n按性别统计:")
        for gender, count in gender_stats.items():
            print(f"  {gender}: {count} 个病例")
        
        # 显示前5行数据
        print(f"\n前5行数据预览:")
        print(clean_df.head().to_string())
        
        return output_file
        
    except Exception as e:
        print(f"处理CSV文件时出错: {e}")
        return None

def create_summary_report():
    """创建数据摘要报告"""
    
    input_file = '../output/vng_patient_data_20250802_210204.csv'
    
    if not os.path.exists(input_file):
        print(f"原始CSV文件不存在: {input_file}")
        return
    
    try:
        df = pd.read_csv(input_file)
        
        # 创建摘要报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f'../output/data_summary_report_{timestamp}.txt'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("VNG病例数据摘要报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据来源: {input_file}\n\n")
            
            f.write(f"总体统计:\n")
            f.write(f"  总病例数: {len(df)}\n")
            f.write(f"  字段数: {len(df.columns)}\n\n")
            
            # 按年份统计
            year_stats = df['年份'].value_counts().sort_index()
            f.write(f"按年份统计:\n")
            for year, count in year_stats.items():
                f.write(f"  {year}年: {count} 个病例\n")
            f.write("\n")
            
            # 按性别统计
            gender_stats = df['性别'].value_counts()
            f.write(f"按性别统计:\n")
            for gender, count in gender_stats.items():
                f.write(f"  {gender}: {count} 个病例\n")
            f.write("\n")
            
            # 检查结果统计
            f.write(f"主要检查结果统计:\n")
            
            # 定标试验
            calibration_stats = df['定标试验'].value_counts()
            f.write(f"  定标试验:\n")
            for result, count in calibration_stats.items():
                if result:  # 非空值
                    f.write(f"    {result}: {count} 例\n")
            
            # 自发性眼震
            spontaneous_stats = df['自发性眼震'].value_counts()
            f.write(f"  自发性眼震:\n")
            for result, count in spontaneous_stats.items():
                if result:  # 非空值
                    f.write(f"    {result}: {count} 例\n")
            
            # 平滑跟踪
            smooth_stats = df['平滑跟踪'].value_counts()
            f.write(f"  平滑跟踪:\n")
            for result, count in smooth_stats.items():
                if result:  # 非空值
                    f.write(f"    {result}: {count} 例\n")
            
            f.write("\n")
            f.write("注: 空值表示该项检查结果未明确记录\n")
        
        print(f"数据摘要报告已保存: {report_file}")
        return report_file
        
    except Exception as e:
        print(f"创建摘要报告时出错: {e}")
        return None

def main():
    """主函数"""
    print("创建清洁版本的VNG数据CSV文件")
    print("=" * 50)
    
    # 创建清洁版本CSV
    clean_file = create_clean_csv()
    
    print("\n" + "=" * 50)
    
    # 创建摘要报告
    report_file = create_summary_report()
    
    print("\n处理完成!")
    if clean_file:
        print(f"清洁版本CSV: {clean_file}")
    if report_file:
        print(f"摘要报告: {report_file}")

if __name__ == "__main__":
    main()
