#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终VNG报告分类脚本

此脚本使用完整的VNG报告验证和日期提取逻辑重新分类所有文档：
1. 只处理包含VNG报告标题的文档
2. 提取检查日期并按年份分类
3. 其他文档放入undefined文件夹

使用方法:
    python final_vng_classification.py [--source-dir SOURCE_DIR] [--target-dir TARGET_DIR] [--debug]
    
作者: Assistant
日期: 2025-01-02
"""

import os
import re
import sys
import shutil
import argparse
from pathlib import Path
import traceback

# 导入必要的库
import re
from datetime import datetime


def is_valid_date_format(date_str):
    """
    验证日期格式是否合理

    参数:
        date_str (str): 日期字符串

    返回:
        bool: 是否为有效日期格式
    """
    if not date_str:
        return False

    # 尝试提取年份并验证
    year_match = re.search(r'(\d{4})', date_str)
    if year_match:
        year = int(year_match.group(1))
        return 1990 <= year <= 2030

    return False


def has_complete_year(date_str):
    """
    检查日期字符串是否包含完整的4位年份

    参数:
        date_str (str): 日期字符串

    返回:
        bool: 是否包含完整的4位年份
    """
    if not date_str:
        return False

    # 检查是否包含4位连续数字作为年份
    year_match = re.search(r'\b(\d{4})\b', date_str)
    if year_match:
        year = int(year_match.group(1))
        # 年份必须在合理范围内
        return 1990 <= year <= 2030

    return False


def is_reasonable_check_date(date_str):
    """
    检查日期是否是合理的检查日期

    参数:
        date_str (str): 日期字符串

    返回:
        bool: 是否是合理的检查日期
    """
    if not date_str:
        return False

    try:
        # 提取年、月、日
        year_match = re.search(r'(\d{4})', date_str)
        if not year_match:
            return False

        year = int(year_match.group(1))

        # 年份必须在合理范围内（2010-2025，因为这是医疗检查报告）
        if not (2010 <= year <= 2025):
            return False

        # 尝试解析完整日期以验证月日的合理性
        date_formats = [
            r'(\d{4})-(\d{1,2})-(\d{1,2})',
            r'(\d{4})/(\d{1,2})/(\d{1,2})',
            r'(\d{4})年(\d{1,2})月(\d{1,2})日'
        ]

        for pattern in date_formats:
            match = re.search(pattern, date_str)
            if match:
                year, month, day = map(int, match.groups())
                # 验证月份和日期的合理性
                if 1 <= month <= 12 and 1 <= day <= 31:
                    # 进一步验证日期的有效性
                    try:
                        datetime(year, month, day)
                        return True
                    except ValueError:
                        continue

        return False

    except Exception:
        return False


def extract_year_from_date(date_str):
    """
    从日期字符串中提取年份

    参数:
        date_str (str): 日期字符串

    返回:
        str: 年份字符串，如果无法提取则返回None
    """
    if not date_str:
        return None

    # 匹配各种日期格式中的年份
    year_patterns = [
        r'(\d{4})[-/年]',  # 2023-01-01, 2023/01/01, 2023年01月01日
        r'^(\d{4})',       # 以4位数字开头
        r'(\d{4})\.',      # 2023.01.01
        r'(\d{4})\s',      # 2023 01 01
    ]

    for pattern in year_patterns:
        match = re.search(pattern, date_str)
        if match:
            year = match.group(1)
            # 验证年份是否合理（1990-2030）
            if 1990 <= int(year) <= 2030:
                return year

    return None


def is_valid_vng_report(full_text, debug=False):
    """
    检查文档是否是有效的VNG报告

    参数:
        full_text (str): 文档全文内容
        debug (bool): 是否显示调试信息

    返回:
        bool: 是否是有效的VNG报告
    """
    # 定义有效的报告标题关键词
    valid_titles = [
        '视频眼震电图（VNG）报告',
        '视频眼震视图（VNG）报告',
        '前庭功能检查报告',
        'VNG'
    ]

    # 检查文档中是否包含任何一个有效标题
    for title in valid_titles:
        if title in full_text:
            if debug:
                print(f"  找到有效报告标题: {title}")
            return True

    if debug:
        print(f"  未找到有效的VNG报告标题")
    return False


def extract_date_from_document_content(file_path, debug=False):
    """
    从文档内容中提取检查日期（仅处理有效的VNG报告）

    参数:
        file_path (str): 文档文件路径
        debug (bool): 是否显示调试信息

    返回:
        str: 提取的日期字符串，如果无法提取或不是有效VNG报告则返回空字符串
    """
    if debug:
        print(f"处理文件: {file_path}")

    file_ext = os.path.splitext(file_path)[1].lower()
    full_text = ""

    try:
        if file_ext == '.docx':
            # 处理DOCX文件
            try:
                from docx import Document
                doc = Document(file_path)

                # 提取段落文本
                for paragraph in doc.paragraphs:
                    full_text += paragraph.text + "\n"

                # 提取表格文本
                for table in doc.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            full_text += cell.text + " "
                    full_text += "\n"

                if debug:
                    print(f"  使用python-docx成功读取DOCX文件")

            except ImportError:
                if debug:
                    print(f"  python-docx未安装")
                return ""
            except Exception as e:
                if debug:
                    print(f"  python-docx无法读取DOCX文件: {e}")
                return ""

        elif file_ext in ['.doc', '.wps']:
            # 处理DOC和WPS文件
            try:
                import olefile
                if olefile.isOleFile(file_path):
                    ole = olefile.OleFileIO(file_path)
                    streams = ole.listdir()
                    if debug:
                        print(f"  OLE文件中的流: {streams}")

                    # 按优先级尝试读取不同的流，优先读取文档内容流
                    priority_streams = ['WordDocument', '1Table', '0Table', 'Data']
                    other_streams = ['/'.join(stream_info) if isinstance(stream_info, list) else str(stream_info)
                                   for stream_info in streams]

                    # 首先尝试优先级高的流
                    for stream_name in priority_streams:
                        if stream_name in [s.replace('\x05', '') for s in other_streams]:
                            try:
                                if ole.exists(stream_name):
                                    stream_data = ole.openstream(stream_name).read()
                                    # 尝试解码为文本
                                    text_data = stream_data.decode('utf-8', errors='ignore')
                                    # 过滤掉大部分二进制字符，只保留可读文本
                                    clean_text = ''.join(char for char in text_data if char.isprintable() or char.isspace())
                                    if len(clean_text.strip()) > 100:  # 需要更多文本才认为有效
                                        full_text += clean_text
                                        if debug:
                                            print(f"  使用olefile从{stream_name}流成功读取文件")
                                        break
                            except Exception as stream_error:
                                if debug:
                                    print(f"  无法读取优先流{stream_name}: {stream_error}")
                                continue

                    # 如果优先流没有足够内容，尝试其他流
                    if len(full_text.strip()) < 100:
                        for stream_info in streams:
                            stream_name = '/'.join(stream_info) if isinstance(stream_info, list) else str(stream_info)
                            # 跳过系统信息流
                            if any(skip in stream_name.lower() for skip in ['summary', 'information', 'compobj']):
                                continue
                            try:
                                if ole.exists(stream_name):
                                    stream_data = ole.openstream(stream_name).read()
                                    text_data = stream_data.decode('utf-8', errors='ignore')
                                    clean_text = ''.join(char for char in text_data if char.isprintable() or char.isspace())
                                    if len(clean_text.strip()) > 100:
                                        full_text += clean_text
                                        if debug:
                                            print(f"  使用olefile从{stream_name}流成功读取文件")
                                        break
                            except Exception as stream_error:
                                if debug:
                                    print(f"  无法读取流{stream_name}: {stream_error}")
                                continue
                    ole.close()
                else:
                    if debug:
                        print(f"  文件不是有效的OLE文件")
                    return ""
            except ImportError:
                if debug:
                    print(f"  olefile未安装")
                return ""
            except Exception as e:
                if debug:
                    print(f"  olefile无法读取文件: {e}")
                return ""
        else:
            if debug:
                print(f"  不支持的文件格式: {file_ext}")
            return ""

        if debug:
            print(f"  提取的文本长度: {len(full_text)}")
            print(f"  文本前200字符: {full_text[:200]}")

        # 首先验证是否是有效的VNG报告
        if not is_valid_vng_report(full_text, debug):
            if debug:
                print("  不是有效的VNG报告，跳过日期提取")
            return ""

        # 改进的日期提取正则表达式 - 处理各种空格和格式变化
        date_patterns = [
            # 优先匹配明确标注为"检查日期"的格式，处理各种空格情况
            r'检查日期\s*[：:]\s*(\d{4}[-/]\d{1,2}[-/]\d{1,2})',
            r'检查日期\s*[：:]\s*(\d{4}年\d{1,2}月\d{1,2}日)',
            r'检\s*查\s*日\s*期\s*[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
            r'检查时间\s*[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',

            # 处理检查日期后有多个空格或其他字符的情况
            r'检查日期\s*[：:]\s+(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
            r'检\s*查\s*日\s*期\s*[：:]\s+(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',

            # 处理不完整年份的情况（如201-5-8应该是2018-5-8）
            r'检查日期\s*[：:]\s*(20\d)[-/](\d{1,2})[-/](\d{1,2})',
            r'检\s*查\s*日\s*期\s*[：:]\s*(20\d)[-/](\d{1,2})[-/](\d{1,2})',

            # 处理检查日期和冒号之间有空格的情况
            r'检查日期\s+[：:]\s*(\d{4}[.\-/年]\d{1,2}[.\-/月]\d{1,2}[日]?)',
            r'检\s*查\s*日\s*期\s+[：:]\s*(\d{4}[.\-/年]\d{1,2}[.\-/月]\d{1,2}[日]?)',

            # 处理没有冒号的情况
            r'检查日期\s+(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
            r'检\s*查\s*日\s*期\s+(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',

            # 特殊格式处理
            r'(20\d{2})(\d{1,2})-(\d{1,2})',  # 201910-09 -> 2019-10-09

            # 文档中常见的标准日期格式（作为备选，但需要验证合理性）
            r'(\d{4}--\d{1,2}-\d{1,2})',  # 2023--10-10 (双横线格式)
            r'(\d{4}-\d{1,2}-\d{1,2})',  # 2024-10-14
            r'(\d{4}/\d{1,2}/\d{1,2})',  # 2024/10/14
            r'(\d{4}年\d{1,2}月\d{1,2}日)',  # 2024年10月14日
        ]

        for pattern in date_patterns:
            match = re.search(pattern, full_text)
            if match:
                # 处理不完整年份的特殊情况
                if len(match.groups()) == 3:  # 三个捕获组的情况（年、月、日分开）
                    year_part, month, day = match.groups()

                    # 处理201910-09这种格式（年份4位+月份连在一起）
                    if len(year_part) == 4 and len(month) <= 2:
                        # 2019 + 10 -> 2019-10
                        date_str = f"{year_part}-{month}-{day}"
                    else:
                        # 修复不完整的年份（如201 -> 2018）
                        if len(year_part) == 3 and year_part.startswith('20'):
                            # 根据上下文推断完整年份，这里假设是2018年
                            year_part = year_part + '8'  # 201 -> 2018
                        date_str = f"{year_part}-{month}-{day}"
                else:
                    date_str = match.group(1)

                # 处理双横线格式 (2023--10-10 -> 2023-10-10)
                if '--' in date_str:
                    date_str = date_str.replace('--', '-')
                    if debug:
                        print(f"  修正双横线格式: {date_str}")

                # 验证日期格式的合理性 - 必须有完整的4位年份且是合理的检查日期
                if is_valid_date_format(date_str) and has_complete_year(date_str) and is_reasonable_check_date(date_str):
                    if debug:
                        print(f"  找到日期: {date_str} (使用模式: {pattern})")
                    return date_str
                elif debug:
                    print(f"  找到但格式无效的日期: {date_str}")

        # 不再从文件名中推断日期 - 严格要求文档内容中有明确的检查日期

        if debug:
            print("  未找到匹配的日期格式")
        return ""

    except Exception as e:
        if debug:
            print(f"  处理文件时发生异常: {e}")
        return ""


def final_vng_classification(source_dir, target_dir, debug=False):
    """
    最终VNG报告分类
    
    参数:
        source_dir (str): 源文件目录
        target_dir (str): 目标分类目录
        debug (bool): 是否显示调试信息
        
    返回:
        dict: 分类结果统计
    """
    if not os.path.exists(source_dir):
        print(f"错误: 源目录不存在 - {source_dir}")
        return None
    
    # 清空目标目录
    if os.path.exists(target_dir):
        shutil.rmtree(target_dir)
    os.makedirs(target_dir)
    
    # 创建undefined目录
    undefined_dir = os.path.join(target_dir, 'undefined')
    os.makedirs(undefined_dir)
    
    # 获取所有文件
    files = []
    for ext in ['.docx', '.doc', '.wps']:
        files.extend([f for f in os.listdir(source_dir) if f.endswith(ext)])
    
    if not files:
        print(f"在目录 {source_dir} 中未找到文件")
        return None
    
    print(f"找到 {len(files)} 个文件需要分类")
    
    # 处理统计
    stats = {
        'total_files': len(files),
        'valid_vng_reports': 0,
        'successfully_classified': 0,
        'moved_to_undefined': 0,
        'year_counts': {},
        'failed_list': []
    }
    
    for i, filename in enumerate(files, 1):
        file_path = os.path.join(source_dir, filename)
        if i % 500 == 0 or i <= 10:
            print(f"[{i}/{len(files)}] 处理文件: {filename}")
        
        try:
            # 使用完整的VNG报告验证和日期提取
            check_date = extract_date_from_document_content(file_path, debug=debug and i <= 5)
            year = extract_year_from_date(check_date) if check_date else None
            
            if year and check_date:
                # 这是有效的VNG报告且有检查日期
                stats['valid_vng_reports'] += 1
                
                # 创建年份目录
                year_dir = os.path.join(target_dir, year)
                if not os.path.exists(year_dir):
                    os.makedirs(year_dir)
                    if i <= 10:
                        print(f"  创建年份目录: {year}")
                
                # 移动文件到年份目录
                target_file = os.path.join(year_dir, filename)
                
                # 如果目标文件已存在，添加序号
                if os.path.exists(target_file):
                    base_name, ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(target_file):
                        new_filename = f"{base_name}_{counter}{ext}"
                        target_file = os.path.join(year_dir, new_filename)
                        counter += 1
                    if i <= 10:
                        print(f"  文件重名，重命名为: {os.path.basename(target_file)}")
                
                shutil.copy2(file_path, target_file)
                if i <= 10:
                    print(f"  分类到: {year}/{os.path.basename(target_file)}")
                
                # 更新统计
                stats['year_counts'][year] = stats['year_counts'].get(year, 0) + 1
                stats['successfully_classified'] += 1
                
            else:
                # 不是有效的VNG报告或没有检查日期，移动到undefined目录
                target_file = os.path.join(undefined_dir, filename)
                
                # 如果目标文件已存在，添加序号
                if os.path.exists(target_file):
                    base_name, ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(target_file):
                        new_filename = f"{base_name}_{counter}{ext}"
                        target_file = os.path.join(undefined_dir, new_filename)
                        counter += 1
                    if i <= 10:
                        print(f"  文件重名，重命名为: {os.path.basename(target_file)}")
                
                shutil.copy2(file_path, target_file)
                if i <= 10:
                    print(f"  移动到: undefined/{os.path.basename(target_file)}")
                stats['moved_to_undefined'] += 1
                
        except Exception as e:
            if i <= 10:
                print(f"  错误: 处理文件时发生异常: {e}")
            if debug:
                print(traceback.format_exc())
            stats['failed_list'].append((filename, str(e)))
            
            # 异常文件也移动到undefined
            try:
                target_file = os.path.join(undefined_dir, filename)
                if os.path.exists(target_file):
                    base_name, ext = os.path.splitext(filename)
                    counter = 1
                    while os.path.exists(target_file):
                        new_filename = f"{base_name}_{counter}{ext}"
                        target_file = os.path.join(undefined_dir, new_filename)
                        counter += 1
                shutil.copy2(file_path, target_file)
                stats['moved_to_undefined'] += 1
            except:
                pass
    
    return stats


def print_final_classification_report(stats):
    """
    打印最终分类结果报告
    
    参数:
        stats (dict): 处理统计结果
    """
    print("\n" + "="*60)
    print("最终VNG报告分类结果")
    print("="*60)
    
    print(f"总文件数: {stats['total_files']:,}")
    print(f"有效VNG报告: {stats['valid_vng_reports']:,}")
    print(f"成功分类: {stats['successfully_classified']:,}")
    print(f"移至undefined: {stats['moved_to_undefined']:,}")
    
    # 计算成功率
    vng_rate = (stats['valid_vng_reports'] / stats['total_files'] * 100) if stats['total_files'] > 0 else 0
    success_rate = (stats['successfully_classified'] / stats['total_files'] * 100) if stats['total_files'] > 0 else 0
    
    print(f"\nVNG报告比例: {vng_rate:.1f}%")
    print(f"分类成功率: {success_rate:.1f}%")
    
    if stats['year_counts']:
        print("\n按年份分类统计:")
        for year in sorted(stats['year_counts'].keys()):
            count = stats['year_counts'][year]
            print(f"  {year}年: {count:,} 个文件")
    
    if stats['failed_list']:
        print(f"\n处理异常的文件 ({len(stats['failed_list'])} 个):")
        for filename, error in stats['failed_list'][:5]:
            print(f"  - {filename}: {error}")
        if len(stats['failed_list']) > 5:
            print(f"  ... 还有 {len(stats['failed_list']) - 5} 个文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='最终VNG报告分类')
    
    # 默认路径
    default_source_dir = "../data/VNG检查报告2024.11.17"
    default_target_dir = "../data/final_vng_classified"

    parser.add_argument('--source-dir',
                       default=default_source_dir,
                       help=f'源文件目录路径 (默认: {default_source_dir})')
    parser.add_argument('--target-dir',
                       default=default_target_dir,
                       help=f'目标分类目录路径 (默认: {default_target_dir})')
    parser.add_argument('--debug', action='store_true', default=True,
                       help='显示调试信息 (默认: 开启)')
    
    args = parser.parse_args()
    
    print("最终VNG报告分类")
    print("只处理包含VNG报告标题的文档，其他文档放入undefined")
    print(f"源目录: {args.source_dir}")
    print(f"目标目录: {args.target_dir}")
    print("-" * 60)
    
    # 执行最终分类
    stats = final_vng_classification(args.source_dir, args.target_dir, args.debug)
    
    if stats:
        print_final_classification_report(stats)
    else:
        print("最终分类失败")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
