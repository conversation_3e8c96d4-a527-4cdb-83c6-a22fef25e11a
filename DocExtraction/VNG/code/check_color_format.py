#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查文档中的颜色标记格式

用于分析格式错误文件中的颜色标记选项
"""

import docx
import os
from docx.shared import RGBColor

def check_color_formatting(file_path):
    """检查文档中的颜色格式"""
    print(f"检查文件: {os.path.basename(file_path)}")
    print("=" * 50)
    
    try:
        doc = docx.Document(file_path)
        
        for para_idx, para in enumerate(doc.paragraphs):
            if para.text.strip():
                print(f"\n段落 {para_idx + 1}: \"{para.text}\"")
                
                # 检查每个run的颜色
                for run_idx, run in enumerate(para.runs):
                    if run.text.strip():
                        color_info = "默认颜色"
                        
                        if run.font.color:
                            if run.font.color.rgb:
                                try:
                                    rgb = run.font.color.rgb
                                    # 处理不同的RGB对象类型
                                    if hasattr(rgb, 'red'):
                                        r, g, b = rgb.red, rgb.green, rgb.blue
                                    else:
                                        # 如果是整数值，需要转换
                                        r = (rgb >> 16) & 0xFF
                                        g = (rgb >> 8) & 0xFF
                                        b = rgb & 0xFF

                                    color_info = f"RGB({r}, {g}, {b})"

                                    # 判断颜色类型
                                    if r > 200 and g < 100 and b < 100:
                                        color_info += " [红色]"
                                    elif r < 100 and g < 100 and b > 200:
                                        color_info += " [蓝色]"
                                    elif r < 100 and g > 200 and b < 100:
                                        color_info += " [绿色]"
                                except Exception as e:
                                    color_info = f"颜色解析错误: {e}"
                                    
                            elif hasattr(run.font.color, 'theme_color') and run.font.color.theme_color:
                                color_info = f"主题颜色: {run.font.color.theme_color}"
                        
                        print(f"  Run {run_idx + 1}: \"{run.text}\" - {color_info}")
                        
        # 检查表格中的颜色
        print(f"\n=== 表格内容 ===")
        for table_idx, table in enumerate(doc.tables):
            print(f"\n表格 {table_idx + 1}:")
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    if cell.text.strip():
                        print(f"  单元格 [{row_idx+1},{cell_idx+1}]: \"{cell.text}\"")
                        
                        # 检查单元格中的颜色
                        for para in cell.paragraphs:
                            for run in para.runs:
                                if run.text.strip() and run.font.color:
                                    if run.font.color.rgb:
                                        try:
                                            rgb = run.font.color.rgb
                                            if hasattr(rgb, 'red'):
                                                r, g, b = rgb.red, rgb.green, rgb.blue
                                            else:
                                                r = (rgb >> 16) & 0xFF
                                                g = (rgb >> 8) & 0xFF
                                                b = rgb & 0xFF
                                            color_info = f"RGB({r}, {g}, {b})"
                                            print(f"    颜色文本: \"{run.text}\" - {color_info}")
                                        except Exception as e:
                                            print(f"    颜色解析错误: {e}")
                        
    except Exception as e:
        print(f"错误: {e}")

def main():
    """主函数"""
    # 检查几个格式错误的文件
    test_files = [
        "../output/failed_files/format_error/188 华雪济.docx",
        "../output/failed_files/format_error/226 苏丽娟.docx",
        "../output/failed_files/format_error/257 谭锡媛.docx"
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            check_color_formatting(file_path)
            print("\n" + "="*80 + "\n")
        else:
            print(f"文件不存在: {file_path}")

if __name__ == "__main__":
    main()
