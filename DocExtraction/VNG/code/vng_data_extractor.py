#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VNG病例信息提取系统

此脚本用于从已分类的VNG病例文档中提取完整的患者信息和检查数据，
包括患者基本信息、VNG检查项目结果、位置试验结果和医生诊断等。

使用方法:
    python vng_data_extractor.py [--source-dir SOURCE_DIR] [--output-dir OUTPUT_DIR] [--debug]
    
作者: Assistant
日期: 2025-01-02
"""

import os
import re
import sys
import csv
import shutil
import argparse
import traceback
from pathlib import Path
from datetime import datetime
import docx
import olefile


class VNGDataExtractor:
    """VNG病例数据提取器"""
    
    def __init__(self, source_dir, output_dir, debug=False):
        self.source_dir = source_dir
        self.output_dir = output_dir
        self.debug = debug
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'failed_files', 'cannot_extract'), exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'failed_files', 'format_error'), exist_ok=True)
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'success_count': 0,
            'failed_count': 0,
            'cannot_extract': 0,
            'format_error': 0,
            'year_stats': {}
        }
        
        # 日志文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = os.path.join(output_dir, f'processing_log_{timestamp}.txt')
        self.csv_file = os.path.join(output_dir, f'vng_patient_data_{timestamp}.csv')
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        
        if self.debug:
            print(log_message)
            
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')
    
    def extract_text_from_file(self, file_path):
        """从文件中提取文本内容"""
        file_ext = os.path.splitext(file_path)[1].lower()
        full_text = ""
        
        try:
            if file_ext == '.docx':
                doc = docx.Document(file_path)
                
                # 提取段落文本
                for paragraph in doc.paragraphs:
                    full_text += paragraph.text + "\n"
                
                # 提取表格文本
                for table in doc.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            full_text += cell.text + " "
                    full_text += "\n"
                    
            elif file_ext in ['.doc', '.wps']:
                if olefile.isOleFile(file_path):
                    ole = olefile.OleFileIO(file_path)
                    streams = ole.listdir()
                    
                    # 尝试读取文档内容流
                    priority_streams = ['WordDocument', '1Table', '0Table', 'Data']
                    
                    for stream_name in priority_streams:
                        try:
                            if ole.exists(stream_name):
                                stream_data = ole.openstream(stream_name).read()
                                text_data = stream_data.decode('utf-8', errors='ignore')
                                clean_text = ''.join(char for char in text_data 
                                                   if char.isprintable() or char.isspace())
                                if len(clean_text.strip()) > 100:
                                    full_text += clean_text
                                    break
                        except Exception:
                            continue
                    ole.close()
                    
        except Exception as e:
            self.log(f"提取文本失败 {file_path}: {e}")
            return ""
            
        return full_text
    
    def extract_by_patterns(self, text, patterns):
        """使用多个正则表达式模式提取信息"""
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        return ""
    
    def extract_basic_info(self, text):
        """提取患者基本信息"""
        info = {}

        # 姓名 - 适配多种格式
        name_patterns = [
            r'姓名__([^\s]+)',  # 姓名__梁趣轩
            r'姓名[_：:]\s*([^\s_]+)',  # 姓名_张三 或 姓名：张三
            r'姓\s*名\s*[_：:]\s*([^\s_]+)',
            r'患者姓名\s*[：:]\s*([^\s]+)',
        ]
        info['姓名'] = self.extract_by_patterns(text, name_patterns)

        # 性别 - 适配下划线格式
        gender_patterns = [
            r'性别\s*[_：:]\s*([男女])',  # 性别  _女
            r'性\s*别\s*[_：:]\s*([男女])',
        ]
        info['性别'] = self.extract_by_patterns(text, gender_patterns)

        # 年龄 - 适配下划线和岁字格式
        age_patterns = [
            r'年龄\s*[_：:]\s*(\d+)[岁]?',  # 年龄  72岁_
            r'年\s*龄\s*[_：:]\s*(\d+)[岁]?',
            r'(\d+)岁',  # 直接匹配数字+岁
        ]
        info['年龄'] = self.extract_by_patterns(text, age_patterns)

        # 检查日期
        date_patterns = [
            r'检\s*查\s*日\s*期\s*[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
            r'检查日期[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
            r'(\d{4}-\d{1,2}-\d{1,2})',
            r'(\d{4}/\d{1,2}/\d{1,2})',
            r'(\d{4}年\d{1,2}月\d{1,2}日)',
        ]
        info['检查日期'] = self.extract_by_patterns(text, date_patterns)

        # 科别 - 适配下划线格式
        dept_patterns = [
            r'科别\s*[_：:]\s*([^_\s\n]{2,20})',  # 科别    _神经一科23床_
            r'科\s*别\s*[_：:]\s*([^_\s\n]{2,20})',
        ]
        info['科别'] = self.extract_by_patterns(text, dept_patterns)

        # 门诊/住院号 - 适配下划线格式
        id_patterns = [
            r'门诊[/／]住院号[_：:]\s*([A-Za-z0-9]+)',  # 门诊/住院号_ 200312 _
            r'住院号[_：:]\s*([A-Za-z0-9]+)',
            r'门诊号[_：:]\s*([A-Za-z0-9]+)',
        ]
        info['门诊住院号'] = self.extract_by_patterns(text, id_patterns)

        # 编号
        number_patterns = [
            r'编\s*号\s*[_：:]\s*([A-Za-z0-9]+)',
            r'编号[_：:]\s*([A-Za-z0-9]+)',
        ]
        info['编号'] = self.extract_by_patterns(text, number_patterns)

        return info
    
    def extract_examination_results(self, text):
        """提取检查结果"""
        results = {}
        
        # 定标试验
        calibration_patterns = [
            r'定标试验\s*([正异]常)',
            r'1、定标试验\s*([正异]常)',
        ]
        results['定标试验'] = self.extract_by_patterns(text, calibration_patterns)
        
        # 自发性眼震
        spontaneous_patterns = [
            r'自发性眼震\s*([无有])',
            r'2、自发性眼震\s*([无有])',
        ]
        results['自发性眼震'] = self.extract_by_patterns(text, spontaneous_patterns)
        
        # 提取自发性眼震的详细信息
        horizontal_left = re.search(r'水平向：左\s*(\d+)%', text)
        results['自发性眼震_水平向左'] = horizontal_left.group(1) if horizontal_left else ""
        
        horizontal_right = re.search(r'右\s*(\d+)%', text)
        results['自发性眼震_水平向右'] = horizontal_right.group(1) if horizontal_right else ""
        
        vertical_up = re.search(r'垂直向：上\s*(\d+)%', text)
        results['自发性眼震_垂直向上'] = vertical_up.group(1) if vertical_up else ""
        
        vertical_down = re.search(r'下\s*(\d+)%', text)
        results['自发性眼震_垂直向下'] = vertical_down.group(1) if vertical_down else ""
        
        # 凝视试验
        gaze_patterns = [
            r'凝视试验\s*([正异]常)',
            r'3、凝视试验\s*([正异]常)',
        ]
        results['凝视试验'] = self.extract_by_patterns(text, gaze_patterns)
        
        # 平滑跟踪
        smooth_patterns = [
            r'平滑跟踪\s*([IⅠⅡⅢⅣ]+型)',
            r'4、平滑跟踪\s*([IⅠⅡⅢⅣ]+型)',
        ]
        results['平滑跟踪'] = self.extract_by_patterns(text, smooth_patterns)
        
        # 扫视试验
        saccade_patterns = [
            r'扫视试验\s*([正异]常)',
            r'5、扫视试验\s*([正异]常)',
        ]
        results['扫视试验'] = self.extract_by_patterns(text, saccade_patterns)
        
        # 扫视试验详情
        saccade_detail = re.search(r'扫视试验\s*[正异]常[（(]([^）)]+)[）)]', text)
        results['扫视试验_详情'] = saccade_detail.group(1) if saccade_detail else ""
        
        # 视动性眼震
        optokinetic_patterns = [
            r'左右向视动反应：([对不]称)',
            r'视动性眼震.*?([对不]称)',
        ]
        results['视动性眼震'] = self.extract_by_patterns(text, optokinetic_patterns)
        
        return results
    
    def extract_position_tests(self, text):
        """提取位置试验结果"""
        results = {}
        
        # Roll Test
        roll_patterns = [
            r'Roll\s*Test\s*([阴阳]性)',
            r'①Roll\s*Test\s*([阴阳]性)',
        ]
        results['Roll_Test'] = self.extract_by_patterns(text, roll_patterns)
        
        # 翻身试验
        turning_patterns = [
            r'翻身试验\s*([阴阳]性)',
            r'②翻身试验\s*([阴阳]性)',
        ]
        results['翻身试验'] = self.extract_by_patterns(text, turning_patterns)
        
        # Dix-Hallpike左侧
        dix_left_patterns = [
            r'左侧悬头位\s*([阴阳]性)',
            r'Dix-Hallpike.*?左侧悬头位\s*([阴阳]性)',
        ]
        results['Dix_Hallpike_左侧'] = self.extract_by_patterns(text, dix_left_patterns)
        
        # Dix-Hallpike右侧
        dix_right_patterns = [
            r'右侧悬头位\s*([阴阳]性)',
            r'Dix-Hallpike.*?右侧悬头位\s*([阴阳]性)',
        ]
        results['Dix_Hallpike_右侧'] = self.extract_by_patterns(text, dix_right_patterns)
        
        # 疲劳现象
        fatigue_patterns = [
            r'疲劳现象\s*([阴阳]性)',
        ]
        results['疲劳现象'] = self.extract_by_patterns(text, fatigue_patterns)
        
        return results
    
    def extract_diagnosis(self, text):
        """提取诊断信息"""
        results = {}
        
        # 印象
        impression_patterns = [
            r'印象[：:]\s*(.*?)(?:检查者|$)',
            r'印象[：:](.*?)(?:\n.*?检查者|$)',
        ]
        impression = self.extract_by_patterns(text, impression_patterns)
        if impression:
            # 清理印象内容
            impression = re.sub(r'\s+', ' ', impression).strip()
            impression = re.sub(r'[\n\r]+', ' ', impression)
        results['印象'] = impression
        
        # 检查者
        examiner_patterns = [
            r'检查者[：:]\s*([^\n]*)',
            r'检查者\s*([^\n]*)',
        ]
        results['检查者'] = self.extract_by_patterns(text, examiner_patterns)
        
        return results

    def extract_patient_data(self, file_path):
        """提取单个病例文件的完整数据"""
        filename = os.path.basename(file_path)

        # 提取年份（从文件路径）
        year = ""
        path_parts = file_path.split(os.sep)
        for part in path_parts:
            if part.isdigit() and len(part) == 4:
                year = part
                break

        # 提取文本内容
        text = self.extract_text_from_file(file_path)
        if not text:
            self.log(f"无法提取文本内容: {filename}")
            return None

        # 验证是否为VNG报告
        vng_keywords = ['视频眼震电图', 'VNG', '前庭功能检查']
        if not any(keyword in text for keyword in vng_keywords):
            self.log(f"非VNG报告: {filename}")
            return None

        # 提取各部分数据
        basic_info = self.extract_basic_info(text)
        exam_results = self.extract_examination_results(text)
        position_tests = self.extract_position_tests(text)
        diagnosis = self.extract_diagnosis(text)

        # 合并所有数据
        patient_data = {
            '文件名': filename,
            '年份': year,
            '原始文本': text[:500] + "..." if len(text) > 500 else text  # 保存前500字符作为参考
        }

        patient_data.update(basic_info)
        patient_data.update(exam_results)
        patient_data.update(position_tests)
        patient_data.update(diagnosis)

        return patient_data

    def validate_data(self, data):
        """验证数据完整性"""
        required_fields = ['姓名', '性别', '年龄', '检查日期']
        missing_fields = []

        for field in required_fields:
            if not data.get(field):
                missing_fields.append(field)

        return len(missing_fields) == 0, missing_fields

    def process_files(self):
        """处理所有文件"""
        self.log("开始处理VNG病例文件...")

        # 获取所有文件，排除undefined文件夹
        all_files = []
        for root, dirs, files in os.walk(self.source_dir):
            # 跳过undefined文件夹
            if 'undefined' in root.lower():
                self.log(f"跳过undefined文件夹: {root}")
                continue

            for file in files:
                if file.endswith(('.docx', '.doc', '.wps')):
                    all_files.append(os.path.join(root, file))

        self.stats['total_files'] = len(all_files)
        self.log(f"找到 {len(all_files)} 个文件需要处理")

        # 准备CSV文件
        csv_headers = [
            '文件名', '年份', '姓名', '性别', '年龄', '检查日期', '科别', '门诊住院号', '编号',
            '定标试验', '自发性眼震', '自发性眼震_水平向左', '自发性眼震_水平向右',
            '自发性眼震_垂直向上', '自发性眼震_垂直向下', '凝视试验', '凝视试验_定向',
            '凝视试验_变向', '平滑跟踪', '扫视试验', '扫视试验_详情', '视动性眼震',
            'Roll_Test', '翻身试验', 'Dix_Hallpike_左侧', 'Dix_Hallpike_右侧', '疲劳现象',
            '印象', '检查者', '原始文本'
        ]

        successful_data = []

        # 处理每个文件
        for i, file_path in enumerate(all_files, 1):
            filename = os.path.basename(file_path)

            if i % 100 == 0 or i <= 10:
                self.log(f"处理进度: {i}/{len(all_files)} - {filename}")

            try:
                # 提取数据
                patient_data = self.extract_patient_data(file_path)

                if patient_data is None:
                    self.stats['cannot_extract'] += 1
                    # 移动到无法提取文件夹
                    dest_path = os.path.join(self.output_dir, 'failed_files', 'cannot_extract', filename)
                    shutil.copy2(file_path, dest_path)
                    continue

                # 验证数据
                is_valid, missing_fields = self.validate_data(patient_data)

                if not is_valid:
                    self.log(f"数据不完整 {filename}: 缺失字段 {missing_fields}")
                    self.stats['format_error'] += 1
                    # 移动到格式错误文件夹
                    dest_path = os.path.join(self.output_dir, 'failed_files', 'format_error', filename)
                    shutil.copy2(file_path, dest_path)
                    continue

                # 数据成功提取
                successful_data.append(patient_data)
                self.stats['success_count'] += 1

                # 统计年份
                year = patient_data.get('年份', 'unknown')
                self.stats['year_stats'][year] = self.stats['year_stats'].get(year, 0) + 1

            except Exception as e:
                self.log(f"处理文件异常 {filename}: {e}")
                if self.debug:
                    self.log(traceback.format_exc())
                self.stats['failed_count'] += 1

                # 移动到格式错误文件夹
                try:
                    dest_path = os.path.join(self.output_dir, 'failed_files', 'format_error', filename)
                    shutil.copy2(file_path, dest_path)
                except:
                    pass

        # 写入CSV文件
        if successful_data:
            self.write_csv(successful_data, csv_headers)
            self.log(f"成功提取 {len(successful_data)} 个病例数据")

        # 生成统计报告
        self.generate_statistics_report()

        return successful_data

    def write_csv(self, data, headers):
        """写入CSV文件"""
        try:
            with open(self.csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=headers, quoting=csv.QUOTE_ALL)
                writer.writeheader()

                for row in data:
                    # 确保所有字段都存在
                    complete_row = {}
                    for header in headers:
                        complete_row[header] = row.get(header, '')
                    writer.writerow(complete_row)

            self.log(f"CSV文件已保存: {self.csv_file}")

        except Exception as e:
            self.log(f"写入CSV文件失败: {e}")

    def generate_statistics_report(self):
        """生成统计报告"""
        report_file = os.path.join(self.output_dir, 'statistics_report.txt')

        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("VNG病例数据提取统计报告\n")
                f.write("=" * 50 + "\n\n")

                f.write(f"总文件数: {self.stats['total_files']}\n")
                f.write(f"成功提取: {self.stats['success_count']}\n")
                f.write(f"无法提取: {self.stats['cannot_extract']}\n")
                f.write(f"格式错误: {self.stats['format_error']}\n")
                f.write(f"处理异常: {self.stats['failed_count']}\n\n")

                success_rate = (self.stats['success_count'] / self.stats['total_files'] * 100) if self.stats['total_files'] > 0 else 0
                f.write(f"成功率: {success_rate:.1f}%\n\n")

                if self.stats['year_stats']:
                    f.write("按年份统计:\n")
                    for year in sorted(self.stats['year_stats'].keys()):
                        count = self.stats['year_stats'][year]
                        f.write(f"  {year}年: {count} 个文件\n")

            self.log(f"统计报告已保存: {report_file}")

        except Exception as e:
            self.log(f"生成统计报告失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='VNG病例信息提取系统')

    # 默认路径
    default_source_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified"
    default_output_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/output"

    parser.add_argument('--source-dir', default=default_source_dir,
                       help=f'源文件目录路径 (默认: {default_source_dir})')
    parser.add_argument('--output-dir', default=default_output_dir,
                       help=f'输出目录路径 (默认: {default_output_dir})')
    parser.add_argument('--debug', action='store_true',
                       help='显示调试信息')

    args = parser.parse_args()

    print("VNG病例信息提取系统")
    print("=" * 50)
    print(f"源目录: {args.source_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"调试模式: {args.debug}")
    print("-" * 50)

    # 检查源目录
    if not os.path.exists(args.source_dir):
        print(f"错误: 源目录不存在 - {args.source_dir}")
        return 1

    # 创建提取器并处理
    extractor = VNGDataExtractor(args.source_dir, args.output_dir, args.debug)

    try:
        results = extractor.process_files()

        print("\n处理完成!")
        print(f"总文件数: {extractor.stats['total_files']}")
        print(f"成功提取: {extractor.stats['success_count']}")
        print(f"成功率: {extractor.stats['success_count']/extractor.stats['total_files']*100:.1f}%")
        print(f"输出文件: {extractor.csv_file}")

        return 0

    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        if args.debug:
            print(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())
